<?php

function Navbar() {
?>
<nav id="navbar" class="bg-[#fefafe] py-3 px-4 flex items-center justify-between sticky top-0 z-50 transition-shadow duration-300 shadow-sm">
  <div class="flex items-center space-x-2">
    <span class="text-xs sm:text-sm text-blue-800 font-semibold">Online</span>
    <span class="text-lg sm:text-xl font-bold text-blue-800">Resume</span>
    <span class="text-lg sm:text-xl text-gray-700">Builder</span>
  </div>

  <div class="flex items-center space-x-4">
    <div class="lg:hidden">
      <button id="mobile-toggle" class="text-gray-700 hover:text-blue-700 focus:outline-none">
        <i class="fas fa-bars w-5 h-5"></i>
      </button>
    </div>

    <div class="hidden lg:flex space-x-4 items-center text-lg relative">
      <a href="/" class="hover:text-blue-700" onmouseenter="setActiveDropdown(null)">Home</a>
      <div class="relative">
        <button id="resume-btn" class="hover:text-blue-700 flex items-center gap-1">
          Resume <i class="fas fa-angle-down"></i>
        </button>
        <div id="resume-menu" class="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-2" style="display: none;">
          <div class="hover:bg-gray-100 rounded">
            <a href="/resume-builder" class="block font-medium text-gray-900 text-lg">Resume Builder</a>
            <p class="text-sm text-gray-500">Create and format resumes with AI editor</p>
          </div>
          <div class="hover:bg-gray-100 rounded mt-3">
            <a href="/resume-samples" class="block text-lg font-medium text-gray-900">Resume Samples</a>
            <p class="text-sm text-gray-500">Build job-tailored resumes with samples</p>
          </div>
        </div>
      </div>
      <div class="relative">
        <button id="cover-btn" class="hover:text-blue-700 flex items-center gap-1">
          Cover Letter <i class="fas fa-angle-down"></i>
        </button>
        <div id="cover-menu" class="absolute top-full left-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-2" style="display: none;">
          <div class="hover:bg-gray-100 rounded">
            <a href="/cover-letter-builder" class="block text-lg font-medium text-gray-900">Cover Letter Builder</a>
            <p class="text-sm text-gray-500">Create and format cover letters with AI editor</p>
          </div>
          <div class="hover:bg-gray-100 rounded mt-3">
            <a href="/cover-letter-samples" class="block text-lg font-medium text-gray-900">Cover Letter Samples</a>
            <p class="text-sm text-gray-500">Build job-tailored cover letters with samples</p>
          </div>
        </div>
      </div>
      <a href="/" class="hover:text-blue-700" onmouseenter="setActiveDropdown(null)">Contact Us</a>
      <a href="/" class="hover:text-blue-700" onmouseenter="setActiveDropdown(null)">About Us</a>
      <div class="h-4 border-l border-gray-300"></div>
      <a href="/signin" class="hover:text-blue-700" onmouseenter="setActiveDropdown(null)">Sign in</a>
    </div>

    <a href="/getstarted" class="hidden sm:block border border-gray-200 text-blue-800 font-semibold rounded-lg px-3 py-1 hover:bg-blue-50 transition text-lg">Get Started</a>
  </div>
</nav>

<div id="mobile-menu" class="lg:hidden fixed left-0 w-full bg-white z-40 p-3 shadow-lg" style="display: none;">
  <div class="flex flex-col">
    <a href="/" class="block py-1 hover:bg-gray-100 text-sm" onclick="toggleMobileMenu()">Home</a>
    <div class="relative">
      <button id="mobile-resume-btn" class="w-full text-left py-1 hover:bg-gray-100 text-sm flex items-center">
        Resume <i class="fas fa-angle-down"></i>
      </button>
      <div id="mobile-resume-menu" class="ml-4" style="display: none;">
        <a href="/resume-builder" class="block py-1 text-xs hover:bg-gray-100" onclick="toggleMobileMenu()">Resume Builder</a>
        <a href="/resume-samples" class="block py-1 text-xs hover:bg-gray-100" onclick="toggleMobileMenu()">Resume Samples</a>
      </div>
    </div>
    <div class="relative">
      <button id="mobile-cover-btn" class="w-full text-left py-1 hover:bg-gray-100 text-sm flex items-center">
        Cover Letter <i class="fas fa-angle-down"></i>
      </button>
      <div id="mobile-cover-menu" class="ml-4" style="display: none;">
        <a href="/cover-letter-builder" class="block py-1 text-xs hover:bg-gray-100" onclick="toggleMobileMenu()">Cover Letter Builder</a>
        <a href="/cover-letter-samples" class="block py-1 text-xs hover:bg-gray-100" onclick="toggleMobileMenu()">Cover Letter Samples</a>
      </div>
    </div>
    <a href="/" class="block py-1 hover:bg-gray-100 text-sm" onclick="toggleMobileMenu()">Contact Us</a>
    <a href="/" class="block py-1 hover:bg-gray-100 text-sm" onclick="toggleMobileMenu()">About Us</a>
    <a href="/signin" class="block py-1 hover:bg-gray-100 text-sm" onclick="toggleMobileMenu()">Sign in</a>
    <a href="/getstarted" class="block py-1 text-blue-800 font-semibold text-sm" onclick="toggleMobileMenu()">Get Started</a>
  </div>
</div>

<script>
  let activeDropdown = null;
  let isScrolled = false;

  const setActiveDropdown = (value) => {
    activeDropdown = value;
    document.getElementById('resume-menu').style.display = activeDropdown === 'resume' ? 'block' : 'none';
    document.getElementById('cover-menu').style.display = activeDropdown === 'coverLetter' ? 'block' : 'none';
  };

  const handleScroll = () => {
    const scrolled = window.scrollY > 0;
    if (scrolled !== isScrolled) {
      isScrolled = scrolled;
      const navbar = document.getElementById('navbar');
      if (isScrolled) {
        navbar.classList.add('shadow-md', 'border-b', 'border-gray-300');
        navbar.classList.remove('shadow-sm');
      } else {
        navbar.classList.add('shadow-sm');
        navbar.classList.remove('shadow-md', 'border-b', 'border-gray-300');
      }
    }
  };
  window.addEventListener('scroll', handleScroll);

  const toggleMobileMenu = () => {
    const mobileMenu = document.getElementById('mobile-menu');
    const toggleBtn = document.getElementById('mobile-toggle');
    const isOpen = mobileMenu.style.display === 'block';
    mobileMenu.style.display = isOpen ? 'none' : 'block';
    document.body.style.overflow = isOpen ? 'auto' : 'hidden';
    const navbarHeight = document.getElementById('navbar').offsetHeight;
    mobileMenu.style.top = `${navbarHeight}px`;
    toggleBtn.innerHTML = isOpen ? '<i class="fas fa-bars w-5 h-5"></i>' : '<i class="fas fa-times w-5 h-5"></i>';
  };
  document.getElementById('mobile-toggle').addEventListener('click', toggleMobileMenu);

  // Desktop dropdowns
  document.getElementById('resume-btn').addEventListener('mouseenter', () => setActiveDropdown('resume'));
  document.getElementById('resume-menu').addEventListener('mouseenter', () => setActiveDropdown('resume'));
  document.getElementById('resume-menu').addEventListener('mouseleave', () => setActiveDropdown(null));

  document.getElementById('cover-btn').addEventListener('mouseenter', () => setActiveDropdown('coverLetter'));
  document.getElementById('cover-menu').addEventListener('mouseenter', () => setActiveDropdown('coverLetter'));
  document.getElementById('cover-menu').addEventListener('mouseleave', () => setActiveDropdown(null));

  // Mobile dropdowns
  document.getElementById('mobile-resume-btn').addEventListener('click', () => {
    const menu = document.getElementById('mobile-resume-menu');
    menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
  });

  document.getElementById('mobile-cover-btn').addEventListener('click', () => {
    const menu = document.getElementById('mobile-cover-menu');
    menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
  });
</script>
<?php
}
?>